import { ReactNode, useEffect, useLayoutEffect, useRef, useState } from "react";
import TabNavigation from "../TabNavigation";
import KeywordTags from "../KeywordTags";
import InfoSection from "../InfoSection";
import { EntityWithDetails, PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import { RowValueWithDetails, RowWithDetails, RowWithValues } from "~/utils/db/entities/rows.db.server";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
// import { LinkedAccountWithDetailsAndMembers } from "~/utils/db/linkedAccounts.db.server";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import LocationDetailsDialog from "~/components/entities/rows/cells/LocationDetailsDialog";
import { useRootData } from "~/utils/data/useRootData";
import PropertyAttributeHelper from "~/utils/helpers/PropertyAttributeHelper";
import { PropertyAttributeName } from "~/application/enums/entities/PropertyAttributeName";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import StringUtils from "~/utils/shared/StringUtils";
import DateUtils from "~/utils/shared/DateUtils";
import MediaItem from "~/components/ui/uploaders/MediaItem";
import { EntityRelationshipWithDetails } from "~/utils/db/entities/entityRelationships.db.server";
import clsx from "clsx";
import RatingBadge from "~/components/ui/badges/RatingBadge";
import { MediaDto } from "~/application/dtos/entities/MediaDto";
import PreviewMediaModal from "~/components/ui/media/PreviewMediaModal";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

const EntityDetails = ({
  entity,
  routes,
  item,
  editing,
  linkedAccounts,
  canDelete,
  canUpdate,
  allEntities,
  onSubmit,
  relationshipRows,
  promptFlows,
  children,
}: {
  entity: EntityWithDetails;
  routes: EntitiesApi.Routes;
  item: RowWithDetails;
  editing: boolean;
  linkedAccounts: any
  canDelete: boolean;
  canUpdate: boolean;
  allEntities: any;
  relationshipRows: RowsApi.GetRelationshipRowsData;
  promptFlows: any;
  onSubmit: (data: FormData) => void;
  children?: ReactNode;
}) => {
  const rootData = useRootData();
  const [activeTab, setActiveTab] = useState("Details");
  const [currentEntity, setCurrentEntity] = useState<EntityWithDetails>(entity);
  const [tabs, setTabs] = useState([{ name: "Details" }]);
  const [groups, setGroups] = useState<{ group?: string; headers: PropertyWithDetails[] }[]>([]);
  const [selectedItem, setSelectedItem] = useState<MediaDto>();
  const [expandedText, setExpandedText] = useState<{ [key: string]: boolean }>({});

  const toggleTextExpansion = (groupId: string) => {
    setExpandedText((prev) => ({
      ...prev,
      [groupId]: !prev[groupId],
    }));
  };



  const { t } = useTranslation();

  function renderRelationshipTab(tabName: string) {
    const parentRelationship = entity.parentEntities.find((r) => r.parent.name === tabName);
    const childRelationship = entity.childEntities.find((r) => r.child.name === tabName);
    const relationship = parentRelationship || childRelationship;

    if (!relationship) return null;

    let relatedRowsData = relatedRows.find((r) => r.relationship.id === relationship.id);

    if (!relatedRowsData?.rows.length) {
      return <div className="mt-10 flex w-full items-center justify-center p-4 text-center text-gray-500">No {tabName} records found.</div>;
    }

    const systemView: any = currentEntity?.views?.find((f) => f.isSystem);
    const systemViewProperties = systemView?.properties;
    if (systemView) {
      currentEntity.properties?.forEach((property) => {
        const systemViewProperty = systemViewProperties?.find((f: any) => f.propertyId === property.id);
        if (systemViewProperty) {
          property.isHidden = false;
        } else {
          property.isHidden = true;
        }
      });
    }

    const getTextLength = ({
      type,
      rowValue,
    }: {
      type: number;
      rowValue: RowValueWithDetails;
    }): number => {
      if (type === PropertyType.TEXT) {
        return rowValue?.textValue?.length || 0;
      }
      return 0;
    };




    return (
      <>
        <div className="px-[14px] py-[16px]">
          <div className="flex flex-col gap-6">
            {/* <div>
              <span className="text-sm font-semibold text-stone-950">{tabName}</span>
            </div> */}
            {relatedRowsData.rows.map((row) => (
              <div
                key={row.id}
                className="rounded-lg border border-solid border-neutral-200 bg-white p-3 text-sm  shadow-[0px_1px_1px_rgba(16,24,40,0.05)]"
              >
                <div className={clsx(
                  "grid gap-4 max-md:grid-cols-1"
                )}>
                  {row.values?.map((value) => {
                    const property = currentEntity?.properties?.find((p: any) => p.id === value.propertyId && !p.isHidden);
                    if (!property) return null;

                    const { type, subType, attributes } = transformHeader(property);
                    const label = property?.title;
                    const isFieldRelationTextEditor =
                      attributes?.some((attribute: any) => ["EditorSize", "editor", "EditorLanguage"].includes(attribute.name)) || false;
                    const isRichText = type === PropertyType.MEDIA || isFieldRelationTextEditor;
                    const dynamicRelationClass = clsx(isRichText ? "col-span-full" : "col-span-1");
                    if (!["ID", "Models.row.folio", "Created at", "Created by"].includes(label)) {
                      return (
                        <div key={value.id} className={dynamicRelationClass}>
                          <InfoSection
                            key={value.id}
                            valueClassName={clsx(!isRichText ? "" : "")}
                            label={label}
                            value={
                              <div
                                className={clsx(
                                  !expandedText[value.id] && "line-clamp-2",
                                  !isRichText && "break-words"
                                )}
                              >
                                {getValueByType({ type, subType, rowValue: value, attributes, label })}
                              </div>
                            }
                          />
                          {isFieldRelationTextEditor && getTextLength({ type, rowValue: value }) > 150 && (
                            <button
                              className="text-xs font-bold underline cursor-pointer text-primary whitespace-nowrap mt-1"
                              onClick={() => toggleTextExpansion(value.id)}
                            >
                              {expandedText[value.id] ? t("shared.readLess") : t("shared.readMore")}

                            </button>
                          )}
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
        {groups.length > 1 && <hr className="mt-4 h-px w-full shrink-0 border border-solid border-zinc-100" />}
      </>
    );
  }

  const transformHeader = (
    property: PropertyWithDetails
  ): { id: string; name: string; type: number; subType: string | null; label: string; value: any; attributes: any } => {
    const attributes = property?.attributes;
    return {
      id: property.id,
      subType: property.subtype,
      type: property.type || 0,
      name: property.name,
      label: StringUtils.capitalize(property.title || property.name),
      value: item.values.find((o) => o.propertyId === property.id),
      attributes: attributes,
    };
  };

  useEffect(() => {
    const groups: { group?: string; headers: PropertyWithDetails[] }[] = [];
    let entityselected = activeTab == "Details" ? entity : allEntities.find((f: any) => f.name === activeTab);
    setCurrentEntity(entityselected);
    entityselected?.properties.forEach((header: any) => {
      const groupName = PropertyAttributeHelper.getPropertyAttributeValue_String(header, PropertyAttributeName.Group);
      let found = groups.find((f) => f.group === groupName);
      if (!found) {
        found = groups.find((f) => !f.group && !groupName);
      }
      if (found) {
        found.headers.push(header);
      } else {
        groups.push({
          group: groupName,
          headers: [header],
        });
      }
    });
    if (groups.length === 0) {
      groups.push({ group: "Details", headers: entity?.properties });
    }
    setGroups(groups);
  }, [entity, item, activeTab]);

  const [relatedRows, setRelatedRows] = useState<{ relationship: EntityRelationshipWithDetails; rows: RowWithValues[] }[]>([]);
  const [hasRelationships, setHasRelationships] = useState(false);


  useEffect(() => {

    const hasAnyRelationships = entity.parentEntities.length > 0 || entity.childEntities.length > 0;
    setHasRelationships(hasAnyRelationships);

    const initialRelatedRows: { relationship: EntityRelationshipWithDetails; rows: RowWithValues[] }[] = [];

    if (item) {
      entity.parentEntities.forEach((relationship) => {
        if (item.parentRows?.length > 0) {
          initialRelatedRows.push({
            relationship,
            rows: item.parentRows.filter((f) => f.relationshipId === relationship.id).map((i) => i.parent),
          });
        }
      });

      entity.childEntities.forEach((relationship) => {
        if (item.childRows?.length > 0) {
          initialRelatedRows.push({
            relationship,
            rows: item.childRows.filter((f) => f.relationshipId === relationship.id).map((i) => i.child),
          });
        }
      });
    }


    const relationCounts: { name: string; count: number }[] = [];


    entity?.parentEntities.forEach((relation) => {
      const name = relation.parent.name;
      const relatedRowsData = initialRelatedRows.find((r) => r.relationship.id === relation.id);
      const count = relatedRowsData?.rows?.length || 0;
      relationCounts.push({ name, count });
    });

    entity?.childEntities.forEach((relation) => {
      const name = relation.child.name;
      const relatedRowsData = initialRelatedRows.find((r) => r.relationship.id === relation.id);
      const count = relatedRowsData?.rows?.length || 0;
      relationCounts.push({ name, count });
    });

    setTabs(relationCounts)

    setRelatedRows(initialRelatedRows);
  }, [entity, item]);

  function download(item: MediaDto) {
    const downloadLink = document.createElement("a");
    downloadLink.href = item.publicUrl ?? item.file;
    downloadLink.download = item.name;
    downloadLink.click();
  }

  function preview(item: MediaDto) {
    setSelectedItem(item);
  }





  const getFormattedTextValue = (subType: string | null, value: string, editorType: string | null, id: string, label?: string) => {
    const naClassName = "!text-foreground !font-medium !text-[14px]";
    if (!value) return <span className={naClassName}>N/A</span>;

    const className = "inline-block items-center gap-5 text-sm text-primary my-auto self-stretch underline decoration-solid decoration-auto underline-offset-auto";
    if (value === "N/A") return <span className={naClassName}>N/A</span>;
    switch (subType) {
      case "email":
        return (
         <div className="flex items-center gap-3 text-sm text-primary group">
            <div>
              <a href={`mailto:${value}`} className={className}>
                {value}
              </a>
            </div>
            <div>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(value);
                  toast("Copied!", {
                    description: "Email copied to clipboard.",
                    position: "top-right",
                    duration: 1500,
                  });
                }}
                className="opacity-0 group-hover:opacity-100 hover:text-muted-foreground transition-opacity cursor-pointer"
                title="Copy Email"
              >
                <svg width="18px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6 11C6 8.17157 6 6.75736 6.87868 5.87868C7.75736 5 9.17157 5 12 5H15C17.8284 5 19.2426 5 20.1213 5.87868C21 6.75736 21 8.17157 21 11V16C21 18.8284 21 20.2426 20.1213 21.1213C19.2426 22 17.8284 22 15 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V11Z" stroke="#1C274C" strokeWidth="0.6" />
                  <path opacity="0.5" d="M6 19C4.34315 19 3 17.6569 3 16V10C3 6.22876 3 4.34315 4.17157 3.17157C5.34315 2 7.22876 2 11 2H15C16.6569 2 18 3.34315 18 5" stroke="#1C274C" strokeWidth="0.6" />
                </svg>
              </button>
            </div>
          </div>
        );
      case "phone":
        return (
        <div className="flex items-center gap-3 text-sm text-primary group">
            <div>
              <a href={`tel:${value}`} className={className}>
                {value}
              </a>
            </div>
            <div>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(value);
                  toast("Copied!", {
                    description: "Phone Number copied to clipboard.",
                    position: "top-right",
                    duration: 1500,
                  });
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                title="Copy phone number"
              >
                <svg width="18px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6 11C6 8.17157 6 6.75736 6.87868 5.87868C7.75736 5 9.17157 5 12 5H15C17.8284 5 19.2426 5 20.1213 5.87868C21 6.75736 21 8.17157 21 11V16C21 18.8284 21 20.2426 20.1213 21.1213C19.2426 22 17.8284 22 15 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V11Z" stroke="#1C274C" strokeWidth="0.6" />
                  <path opacity="0.5" d="M6 19C4.34315 19 3 17.6569 3 16V10C3 6.22876 3 4.34315 4.17157 3.17157C5.34315 2 7.22876 2 11 2H15C16.6569 2 18 3.34315 18 5" stroke="#1C274C" strokeWidth="0.6" />
                </svg>
              </button>
            </div>
          </div>
        );
      case "url":
        return (
          <div className="flex items-center gap-5 text-sm text-primary">
            <a href={value} rel="noreferrer" target="_blank" className={className}>
              {label ? `View ${label}` : "View"}
            </a>
          </div>
        );
      default:
        const isEditor = editorType && ["EditorSize", "editor", "EditorLanguage", "monaco"].includes(editorType);
        const isLongText = value.length > 150;
        const isExpanded = expandedText[id];

        return (
          <div>
            {isEditor ? (
              <div
                className={clsx(
                  !isExpanded && "line-clamp-2"
                )}
                dangerouslySetInnerHTML={{ __html: value }}
              />
            ) : (
              <div className="break-words">{value}</div>
            )}

            {isEditor && isLongText && (
              <button
                className="text-xs font-bold underline cursor-pointer text-primary mt-1"
                onClick={() => toggleTextExpansion(id)}
              >
                {isExpanded ? t("shared.readLess") : t("shared.readMore")}
              </button>
            )}
          </div>
        );
    }
  };

  const getFormattedMultiText = (rowValue: RowValueWithDetails) => {
    const naClassName = "!text-foreground !font-medium !text-[14px]";
    if (!rowValue?.multiple?.length) return <span className={naClassName}>N/A</span>;

    let textChips = rowValue?.multiple?.map((o: any) => o.value);
    return <KeywordTags tags={textChips} />;
  };

  const getFormattedSingleSelect = (rowValue: RowValueWithDetails) => {
    const naClassName = "!text-foreground !font-medium !text-[14px]";
    if (!rowValue?.textValue) return <span className={naClassName}>N/A</span>;

    return <KeywordTags tags={[rowValue?.textValue]} />;
  };

  const getFormattedMultiple = (rowValue: RowValueWithDetails) => {
    const naClassName = "!text-foreground !font-medium !text-[14px]";
    if (!rowValue?.multiple?.length) return <span className={naClassName}>N/A</span>;

    let multitextValues = rowValue?.multiple?.map((item) => item.value);
    return <KeywordTags tags={multitextValues} />;
  };

  const getValueByType = ({
    type,
    subType,
    rowValue,
    attributes,
    label,
  }: {
    type: number;
    subType: string | null;
    rowValue: RowValueWithDetails;
    attributes: any;
    label?: string;
  }): any => {
    const isRating = attributes?.find((item: any) => item.value === "rating") ? true : false;

    if (isRating) {
      return (
        <span>
          <RatingBadge value={Number(rowValue?.numberValue)} />
        </span>
      );
    }

    switch (type) {
      case PropertyType.TEXT:
        const editorType = attributes.find((item: any) => item.name === "editor")?.value;
        return getFormattedTextValue(subType, rowValue?.textValue || "N/A", editorType, rowValue?.id, label);

      case PropertyType.BOOLEAN:
        return typeof rowValue?.booleanValue === "boolean" ? (rowValue.booleanValue ? "Yes" : "No") : "N/A";

      case PropertyType.NUMBER:
        return rowValue?.numberValue || "N/A";

      case PropertyType.DATE:
        return rowValue?.dateValue ? DateUtils.dateMonthDayYear(rowValue.dateValue) : "N/A";
      case PropertyType.TIME:
        if (!rowValue?.dateValue) return "N/A";
        // Check if the property has a 12h subtype for AM/PM format
        const is12HourFormat = subType === "12h";
        return is12HourFormat ? DateUtils.timeHM12(rowValue.dateValue) : DateUtils.timeHM(rowValue.dateValue);
      case PropertyType.DATE_TIME:
        if (!rowValue?.dateValue) return "N/A";
        // Check if the property has a 12h subtype for AM/PM format
        const is12HourFormatDateTime = subType === "12h";
        const dateStr = DateUtils.dateMonthDayYear(rowValue.dateValue);
        const timeStr = is12HourFormatDateTime ? DateUtils.timeHM12(rowValue.dateValue) : DateUtils.timeHM(rowValue.dateValue);
        return `${dateStr} ${timeStr}`;
      case PropertyType.RANGE_DATE:
        const minDate = rowValue?.range?.dateMin ? DateUtils.dateMonthDayYear(rowValue?.range?.dateMin) : "N/A";
        const maxDate = rowValue?.range?.dateMax ? DateUtils.dateMonthDayYear(rowValue?.range?.dateMax) : "N/A";
        return `${minDate} - ${maxDate}`;

      case PropertyType.TIME_RANGE:
        if (!rowValue?.range?.dateMin || !rowValue?.range?.dateMax) return "N/A";

        // Convert string dates to Date objects if needed
        const startDate = rowValue.range.dateMin instanceof Date ? rowValue.range.dateMin : new Date(rowValue.range.dateMin);
        const endDate = rowValue.range.dateMax instanceof Date ? rowValue.range.dateMax : new Date(rowValue.range.dateMax);

        // Validate dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return "N/A";

        // Check if the property has a 12h subtype for AM/PM format
        const is12HourFormatTimeRange = subType === "12h";
        const startTime = is12HourFormatTimeRange ? DateUtils.timeHM12(startDate) : DateUtils.timeHM(startDate);
        const endTime = is12HourFormatTimeRange ? DateUtils.timeHM12(endDate) : DateUtils.timeHM(endDate);

        // Check if it's an overnight range
        const startMinutes = startDate.getHours() * 60 + startDate.getMinutes();
        const endMinutes = endDate.getHours() * 60 + endDate.getMinutes();
        const isOvernight = endMinutes <= startMinutes;

        return isOvernight ? `${startTime} – ${endTime} (+1 day)` : `${startTime} – ${endTime}`;

      case PropertyType.MEDIA:
        return rowValue?.media ? (
          <>
            <MediaItem
              showReuploadButton={false}
              item={{
                id: rowValue?.media?.[0]?.id || "",
                title: rowValue?.media?.[0]?.title || "",
                name: rowValue?.media?.[0]?.name || "",
                file: rowValue?.media?.[0]?.file || "",
                type: rowValue?.media?.[0]?.type || "",
                publicUrl: rowValue?.media?.[0]?.publicUrl || undefined,
                storageBucket: rowValue?.media?.[0]?.storageBucket || undefined,
                storageProvider: rowValue?.media?.[0]?.storageProvider || undefined,
              }}
              onChangeTitle={function (e: string): void {
                throw new Error("Function not implemented.");
              }}
              onDelete={function (): void {
                throw new Error("Function not implemented.");
              }}
              onDownload={() => {
                const mediaItem = rowValue?.media?.[0];
                if (mediaItem) {
                  download(mediaItem);
                } else {
                  console.error("Media item is missing required properties.");
                }
              }}


              onPreview={
                rowValue?.media?.[0].type && (rowValue?.media?.[0].type.includes("pdf") || rowValue?.media?.[0].type.includes("image"))
                  ? () => preview(rowValue?.media?.[0])
                  : undefined
              }


            />
          </>
        ) : (
          "N/A"
        );

      case PropertyType.MULTI_SELECT:
        return getFormattedMultiple(rowValue);
      case PropertyType.MULTI_TEXT:
        return getFormattedMultiText(rowValue);

      case PropertyType.RANGE_NUMBER:
        return rowValue?.range?.numberMin && rowValue?.range?.numberMax ? `${rowValue?.range?.numberMin} - ${rowValue?.range?.numberMax}` : "N/A";
      case PropertyType.SELECT:
        return getFormattedSingleSelect(rowValue);
      case PropertyType.LOCATION:
        // For location properties, we'll handle them separately to show individual fields
        return rowValue?.textValue ? JSON.parse(rowValue?.textValue)?.formattedAddress || JSON.parse(rowValue?.textValue)?.address || "Location data" : "No location data";

      default:
        return "Type N/A";
    }
  };

  const tabCounts: Record<string, number> = {};
  entity.parentEntities.forEach((relation) => {
    const relatedRowsData = relatedRows.find((r) => r.relationship.id === relation.id);
    const count = relatedRowsData?.rows?.length || 0;
    tabCounts[relation.parent.name] = count;
  });

  entity.childEntities.forEach((relation) => {
    const relatedRowsData = relatedRows.find((r) => r.relationship.id === relation.id);
    const count = relatedRowsData?.rows?.length || 0;
    tabCounts[relation.child.name] = count;
  });

  const checkGroup = (group: any) => {
    if (group.group) return true;
    const allLabelsExcluded = group.headers.every((groupHeader: any) => {
      const { label } = transformHeader(groupHeader);
      return ["ID", "Models.row.folio", "Created at", "Created by"].includes(label);
    });

    return !allLabelsExcluded;
  };


  const renderActiveTabView = () => {
    switch (activeTab) {
      case "Details": {
        return groups.map((g, gIndex) => {
          if (!g.group && !checkGroup(g)) return null;
          return (<>
            <div className="flex max-w-full flex-col overflow-hidden rounded-[8px] border border-solid border-input bg-card m-3 ">
              {g.group ? (
                <section key={g.group} className=" w-full self-center  text-sm max-md:max-w-full px-5 pt-5 pb-4">
                  <h2 className="self-start  text-foreground font-bold text-[14px] leading-[100%] tracking-[0%]">{g.group}</h2>
                </section>
              ) : <section key={g.group} className=" w-full self-center  text-sm max-md:max-w-full px-5 pt-5 pb-4">
                <h2 className="self-start  text-foreground font-bold text-[14px] leading-[100%] tracking-[0%]">General Details</h2>
              </section>}
              <section className={clsx(
                "w-full self-center px-5 text-sm max-md:max-w-full pb-5 grid gap-6",
              )}>
                {g.headers.map((groupHeader) => {
                  const { id, label, type, subType, value, attributes } = transformHeader(groupHeader);
                  const isFieldTextEditor = attributes?.some((attribute: any) => ["EditorSize", "editor", "EditorLanguage"].includes(attribute.name)) || false;

                  // Special handling for location properties - show formatted address with dialog
                  if (type === PropertyType.LOCATION && value?.textValue) {
                    try {
                      const locationData = JSON.parse(value.textValue);

                      return (
                        <div key={id} className="col-span-1">
                          <InfoSection
                            label={label}
                            value={
                              <LocationDetailsDialog
                                value={value.textValue}
                                title={label}
                                apiKey={rootData.googleMapsApiKey}
                              />
                            }
                          />
                        </div>
                      );
                    } catch (error) {
                      // Fallback to regular display if parsing fails
                      const dynamicClass = "col-span-1";
                      return !["ID", "Models.row.folio", "Created at", "Created by"].includes(label) ? (
                        <div key={id} className={dynamicClass}>
                          <InfoSection label={label} value={getValueByType({ type, subType, rowValue: value, attributes, label })} />
                        </div>
                      ) : null;
                    }
                  }

                  // Regular handling for non-location properties
                  const dynamicClass = clsx(
                    (type === PropertyType.MEDIA || isFieldTextEditor || type === PropertyType.BOOLEAN)
                      ? "col-span-full"
                      : "col-span-1"
                  );
                  return !["ID", "Models.row.folio", "Created at", "Created by"].includes(label) ? (
                    <div key={id} className={dynamicClass}>
                      <InfoSection label={label} value={getValueByType({ type, subType, rowValue: value, attributes, label })} />
                    </div>
                  ) : null;
                })}
              </section>
              {gIndex < groups.length - 1 ? <hr className="mt-4 h-px w-full shrink-0 border border-solid border-zinc-100" /> : null}
            </div>
          </>)
        });
      }
      default: {
        return renderRelationshipTab(activeTab);
      }
    }
  };

  return (
    <>
      <article

        className="pb-4">
        {hasRelationships && (
          <TabNavigation
            tabs={[{ name: "Details" }, ...tabs]}
            activeTab={activeTab}
            className="shadow-md border border-input rounded-[8px] !pl-5"
            onTabChange={setActiveTab}
          />
        )}
        {renderActiveTabView()}

      </article>
      {selectedItem && (
        <PreviewMediaModal
          item={selectedItem}
          onClose={() => setSelectedItem(undefined)}
          onDownload={() => download(selectedItem)}
        />
      )}
    </>
  );
};

export default EntityDetails;
